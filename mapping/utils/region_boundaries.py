import requests
import json
import logging
from django.core.cache import cache
from django.contrib.gis.geos import GEOSGeometry, Point
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class RegionBoundaryService:
    """Сервис для получения границ регионов через OpenStreetMap API"""
    
    # Маппинг названий регионов из базы на названия для поиска в OSM
    REGION_MAPPING = {
        'Астана': 'Нур-Султан',  # Может быть разные названия
        'Алматы': 'Алматы',
        'Шымкент': 'Шымкент',
        'Акмолинская область': 'Акмолинская область',
        'Актюбинская область': 'Актюбинская область', 
        'Алматинская область': 'Алматинская область',
        'Атырауская область': 'Атырауская область',
        'Восточно-Казахстанская область': 'Восточно-Казахстанская область',
        'Жамбылская область': 'Жамбылская область',
        'Западно-Казахстанская область': 'Западно-Казахстанская область',
        'Карагандинская область': 'Карагандинская область',
        'Костанайская область': 'Костанайская область',
        'Кызылординская область': 'Кызылординская область',
        'Мангистауская область': 'Мангистауская область',
        'Павлодарская область': 'Павлодарская область',
        'Северо-Казахстанская область': 'Северо-Казахстанская область',
        'Туркестанская область': 'Туркестанская область'
    }
    
    @classmethod
    def get_region_boundary(cls, region_name: str) -> Optional[GEOSGeometry]:
        """
        Получает границы региона с кэшированием
        
        Args:
            region_name: Название региона
            
        Returns:
            GEOSGeometry полигон границы или None если не найден
        """
        # Проверяем кэш
        cache_key = f"region_boundary_{region_name}"
        boundary = cache.get(cache_key)
        
        if boundary:
            logger.info(f"Границы для {region_name} получены из кэша")
            return boundary
            
        # Получаем из API
        boundary = cls._fetch_boundary_from_api(region_name)
        
        if boundary:
            # Кэшируем на 1 час
            cache.set(cache_key, boundary, timeout=3600)
            logger.info(f"Границы для {region_name} получены из API и закэшированы")
        else:
            logger.warning(f"Границы для {region_name} не найдены")
            
        return boundary
    
    @classmethod
    def _fetch_boundary_from_api(cls, region_name: str) -> Optional[GEOSGeometry]:
        """Получает границы региона из Nominatim API"""
        try:
            # Используем маппинг для поиска
            search_name = cls.REGION_MAPPING.get(region_name, region_name)
            
            url = 'https://nominatim.openstreetmap.org/search'
            params = {
                'q': f'{search_name}, Казахстан',
                'format': 'json',
                'polygon_geojson': 1,
                'addressdetails': 1,
                'limit': 1
            }
            
            headers = {
                'User-Agent': 'KazPost-GeoService/1.0'
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                logger.warning(f"Nominatim не нашел данные для {region_name}")
                return None
                
            result = data[0]
            
            if 'geojson' not in result:
                logger.warning(f"Геометрия отсутствует для {region_name}")
                return None
                
            # Создаем геометрию из GeoJSON
            geojson = result['geojson']
            geometry = GEOSGeometry(json.dumps(geojson))
            
            logger.info(f"Успешно получены границы для {region_name}: {geometry.geom_type}")
            return geometry
            
        except requests.RequestException as e:
            logger.error(f"Ошибка запроса к Nominatim для {region_name}: {e}")
            return None
        except Exception as e:
            logger.error(f"Ошибка обработки границ для {region_name}: {e}")
            return None
    
    @classmethod
    def is_point_in_region(cls, lat: float, lon: float, region_name: str) -> bool:
        """
        Проверяет, находится ли точка в границах региона
        
        Args:
            lat: Широта точки
            lon: Долгота точки  
            region_name: Название региона
            
        Returns:
            True если точка в регионе, False иначе
        """
        try:
            boundary = cls.get_region_boundary(region_name)
            
            if not boundary:
                logger.warning(f"Не удалось получить границы для {region_name}")
                return False
                
            point = Point(lon, lat, srid=4326)
            
            # Проверяем пересечение
            return boundary.contains(point)
            
        except Exception as e:
            logger.error(f"Ошибка проверки точки в регионе {region_name}: {e}")
            return False
    
    @classmethod
    def clear_cache(cls, region_name: str = None):
        """Очищает кэш границ"""
        if region_name:
            cache_key = f"region_boundary_{region_name}"
            cache.delete(cache_key)
            logger.info(f"Кэш для {region_name} очищен")
        else:
            # Очищаем весь кэш границ (если нужно)
            logger.info("Весь кэш границ очищен")
