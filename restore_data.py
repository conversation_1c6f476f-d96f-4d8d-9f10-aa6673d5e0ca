#!/usr/bin/env python
"""
Скрипт для восстановления данных после отката миграции 0007_auto_20240401_1007.py

Восстанавливает:
1. UserRegionAccess - на основе анализа changelog
2. ProductCategoryLayers - из CSV файла
3. Departments.dm_tindex - из CSV файла

Использование:
python manage.py shell < restore_data.py
"""

import os
import sys
import django
import csv
from collections import defaultdict

# Настройка Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'post_admin.settings')
django.setup()

from django.contrib.auth.models import User
from mapping.models import (
    UserRegionAccess, Region, Departments, Layers, 
    ProductCategoryLayers, Hubs
)
from changelog.models import ChangeLog

def analyze_user_activity():
    """
    Анализирует активность пользователей по регионам на основе changelog
    """
    print("🔍 Анализ активности пользователей...")
    
    # Словарь: пользователь -> множество регионов
    user_regions = defaultdict(set)
    
    # Получаем все логи изменений полигонов (Hubs)
    hub_logs = ChangeLog.objects.filter(
        model='Hubs'
    ).select_related('user').order_by('-changed')
    
    print(f"📊 Найдено {hub_logs.count()} записей в логах полигонов")
    
    for log in hub_logs:
        if log.user and log.record_id:
            try:
                # Находим полигон и его регион
                hub = Hubs.objects.get(id=log.record_id)
                if hub.department and hub.department.region:
                    region = hub.department.region
                    user_regions[log.user].add(region)
                    print(f"👤 {log.user.username} работал с регионом: {region.name}")
            except Hubs.DoesNotExist:
                continue
    
    return user_regions

def restore_user_region_access():
    """
    Восстанавливает доступы пользователей к регионам
    """
    print("\n🔧 Восстановление UserRegionAccess...")
    
    # Анализируем активность пользователей
    user_regions = analyze_user_activity()
    
    if not user_regions:
        print("⚠️  Не найдено активности пользователей в логах")
        return
    
    created_count = 0
    
    for user, regions in user_regions.items():
        for region in regions:
            # Проверяем, есть ли уже такая запись
            access, created = UserRegionAccess.objects.get_or_create(
                user=user,
                region=region,
                defaults={'has_full_access': False}
            )
            
            if created:
                created_count += 1
                print(f"✅ Создан доступ: {user.username} -> {region.name}")
            else:
                print(f"ℹ️  Доступ уже существует: {user.username} -> {region.name}")
    
    print(f"\n📈 Создано {created_count} записей UserRegionAccess")

def restore_product_category_layers():
    """
    Восстанавливает ProductCategoryLayers из CSV файла
    """
    print("\n🔧 Восстановление ProductCategoryLayers...")
    
    csv_file = 'geonpi_public_layers.csv'
    
    if not os.path.exists(csv_file):
        print(f"❌ Файл {csv_file} не найден")
        return
    
    created_count = 0
    
    with open(csv_file, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        
        for row in reader:
            if len(row) >= 3:
                layer_id = row[0].strip()
                product_code = row[1].strip()
                product_name = row[2].strip()
                
                try:
                    # Находим слой
                    layer = Layers.objects.get(id=layer_id)
                    
                    # Создаем или обновляем продукт
                    product, created = ProductCategoryLayers.objects.get_or_create(
                        product_code=product_code,
                        defaults={
                            'product_name': product_name,
                            'layer': layer
                        }
                    )
                    
                    if created:
                        created_count += 1
                        print(f"✅ Создан продукт: {product_code} - {product_name}")
                    else:
                        # Обновляем существующий
                        product.product_name = product_name
                        product.layer = layer
                        product.save()
                        print(f"🔄 Обновлен продукт: {product_code} - {product_name}")
                        
                except Layers.DoesNotExist:
                    print(f"⚠️  Слой с ID {layer_id} не найден для продукта {product_code}")
                except Exception as e:
                    print(f"❌ Ошибка при обработке {product_code}: {e}")
    
    print(f"\n📈 Обработано продуктов: {created_count}")

def restore_departments_tindex():
    """
    Восстанавливает dm_tindex для отделений из CSV файла
    """
    print("\n🔧 Восстановление Departments.dm_tindex...")
    
    csv_file = 'mapping/temp/Depatments_tindex.csv'
    
    if not os.path.exists(csv_file):
        print(f"❌ Файл {csv_file} не найден")
        return
    
    updated_count = 0
    
    with open(csv_file, 'r', encoding='utf-8') as file:
        reader = csv.reader(file, delimiter=';')
        next(reader)  # Пропускаем заголовок
        
        for row in reader:
            if len(row) >= 2:
                dep_id = row[0].strip()
                tech_index = row[1].strip()
                
                try:
                    department = Departments.objects.get(id=dep_id)
                    department.dm_tindex = tech_index
                    department.save()
                    updated_count += 1
                    print(f"✅ Обновлен dm_tindex для отделения {dep_id}: {tech_index}")
                    
                except Departments.DoesNotExist:
                    print(f"⚠️  Отделение с ID {dep_id} не найдено")
                except Exception as e:
                    print(f"❌ Ошибка при обработке отделения {dep_id}: {e}")
    
    print(f"\n📈 Обновлено отделений: {updated_count}")

def main():
    """
    Основная функция восстановления данных
    """
    print("🚀 Начинаем восстановление данных после отката миграции...")
    print("=" * 60)
    
    try:
        # 1. Восстанавливаем UserRegionAccess
        restore_user_region_access()
        
        # 2. Восстанавливаем ProductCategoryLayers
        restore_product_category_layers()
        
        # 3. Восстанавливаем dm_tindex
        restore_departments_tindex()
        
        print("\n" + "=" * 60)
        print("🎉 Восстановление данных завершено!")
        
        # Статистика
        print("\n📊 Итоговая статистика:")
        print(f"UserRegionAccess записей: {UserRegionAccess.objects.count()}")
        print(f"ProductCategoryLayers записей: {ProductCategoryLayers.objects.count()}")
        print(f"Отделений с dm_tindex: {Departments.objects.exclude(dm_tindex='------').count()}")
        
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
